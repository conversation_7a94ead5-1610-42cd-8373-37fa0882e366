<template>
  <nav class="header">
    <div class="header-container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo">
          <Mail :size="32" />
          <span class="logo-text">MailCode</span>
        </div>

        <!-- Navigation Links -->
        <div class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/get-mailbox" class="nav-link">获取邮箱</router-link>
          <router-link to="/my-mailbox" class="nav-link">我的邮箱</router-link>
        </div>

        <!-- Action Buttons -->
        <div class="header-actions">
          <!-- 未登录状态 -->
          <template v-if="!userStore.isLoggedIn">
            <t-button variant="text" @click="handleLogin">登录</t-button>
            <t-button theme="primary" @click="handleRegister">注册</t-button>
          </template>

          <!-- 已登录状态 -->
          <template v-else>
            <t-dropdown :options="userMenuOptions" @click="handleUserMenuClick">
              <div class="user-menu-btn">
                <CircleUser :size="32" />
                <span class="username">{{ userStore.username }}</span>
                <ChevronDown :size="20" />
              </div>
            </t-dropdown>
          </template>

          <t-button variant="text" class="mobile-menu-btn" @click="toggleMobileMenu">
            <Menu :size="20" />
          </t-button>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-if="showMobileMenu" class="mobile-menu">
      <router-link to="/" class="mobile-nav-link" @click="closeMobileMenu">首页</router-link>
      <router-link to="/get-mailbox" class="mobile-nav-link" @click="closeMobileMenu">获取邮箱</router-link>
      <router-link to="/my-mailbox" class="mobile-nav-link" @click="closeMobileMenu">我的邮箱</router-link>

      <!-- 移动端用户菜单 -->
      <div v-if="userStore.isLoggedIn" class="mobile-user-section">
        <div class="mobile-user-info">
          <CircleUser :size="28" />
          <span>{{ userStore.username }}</span>
        </div>
        <div class="mobile-user-actions">
          <button class="mobile-action-btn" @click="handleUserMenuClick({value: 'profile'})">
            <User :size="16" />
            个人设置
          </button>
          <button class="mobile-action-btn logout-btn" @click="handleUserMenuClick({value: 'logout'})">
            <LogOut :size="16" />
            退出登录
          </button>
        </div>
      </div>

      <!-- 移动端登录按钮 -->
      <div v-else class="mobile-auth-section">
        <button class="mobile-auth-btn login-btn" @click="handleLogin">登录</button>
        <button class="mobile-auth-btn register-btn" @click="handleRegister">注册</button>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import { MessagePlugin } from 'tdesign-vue-next'
import { Mail, CircleUser, ChevronDown, Menu, User, LogOut } from 'lucide-vue-next'

const router = useRouter()
const userStore = useUserStore()
const showMobileMenu = ref(false)

// 用户菜单选项
const userMenuOptions = computed(() => [
  {
    content: '我的邮箱',
    value: 'my-mailbox',
    prefixIcon: () => h(Mail, { size: 20 })
  },
  {
    content: '个人设置',
    value: 'profile',
    prefixIcon: () => h(User, { size: 20 })
  },
  {
    content: '退出登录',
    value: 'logout',
    prefixIcon: () => h(LogOut, { size: 20 })
  }
])

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleLogin = () => {
  router.push('/login')
}

const handleRegister = () => {
  router.push('/register')
}

// 处理用户菜单点击
const handleUserMenuClick = async (data) => {
  switch (data.value) {
    case 'my-mailbox':
      router.push('/my-mailbox')
      break
    case 'profile':
      // TODO: 跳转到个人设置页面
      MessagePlugin.info('个人设置功能即将上线')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    userStore.logout()
    await MessagePlugin.success('退出登录成功')
    router.push('/')
  } catch (error) {
    console.error('退出登录失败:', error)
    await MessagePlugin.error('退出登录失败')
  }
}
</script>

<style lang="less" scoped>
.header {
  border-bottom: 1px solid #e5e7eb;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;

  svg {
    color: #3B82F6;
  }

  .logo-text {
    font-size: 20px;
    font-weight: bold;
    color: #111827;
  }
}

.nav-links {
  display: none;
  align-items: center;
  gap: 32px;

  @media (min-width: 768px) {
    display: flex;
  }
}

.nav-link {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.2s;

  &:hover {
    color: #111827;
  }

  &.router-link-active {
    color: #2563eb;
    font-weight: 500;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-menu-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  background: transparent;

  .username {
    font-weight: 500;
    color: #374151;
    font-size: 18px;
    line-height: 32px;

    @media (max-width: 640px) {
      display: none;
    }
  }

  svg {
    color: #6b7280;
    transition: color 0.2s ease;
    flex-shrink: 0;
  }

  &:hover {
    background-color: #f9fafb;

    .username {
      color: #111827;
    }

    svg {
      color: #374151;
    }
  }
}

.mobile-menu-btn {
  display: block;

  @media (min-width: 768px) {
    display: none;
  }
}

.mobile-menu {
  display: block;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 16px;

  @media (min-width: 768px) {
    display: none;
  }
}

.mobile-nav-link {
  display: block;
  padding: 12px 0;
  color: #6b7280;
  text-decoration: none;
  border-bottom: 1px solid #f3f4f6;

  &:hover {
    color: #111827;
  }

  &.router-link-active {
    color: #2563eb;
    font-weight: 500;
  }
}

.mobile-user-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
  margin-top: 16px;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  color: #374151;
  font-weight: 500;
  font-size: 16px;
  border-bottom: 1px solid #f3f4f6;

  svg {
    color: #6b7280;
  }
}

.mobile-user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 12px;
}

.mobile-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  background: none;
  border: none;
  color: #6b7280;
  text-align: left;
  cursor: pointer;
  transition: color 0.2s;

  &:hover {
    color: #111827;
  }

  &.logout-btn {
    color: #dc2626;

    &:hover {
      color: #b91c1c;
    }
  }
}

.mobile-auth-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.mobile-auth-btn {
  flex: 1;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;

  &.login-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  &.register-btn {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;

    &:hover {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
}

/* 下拉菜单样式覆盖 - 强制覆盖所有可能的样式 */
:deep(.t-dropdown__menu),
:deep(.t-dropdown__menu-wrapper .t-dropdown__menu),
:deep(.t-popup__content .t-dropdown__menu) {
  width: 160px !important;
  min-width: 160px !important;
  max-width: none !important;
}

:deep(.t-dropdown__item),
:deep(.t-dropdown__menu .t-dropdown__item) {
  font-size: 16px !important;
  padding: 14px 16px !important;
  min-height: 48px !important;
  line-height: 1.4 !important;
  white-space: nowrap !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

:deep(.t-dropdown__item-content),
:deep(.t-dropdown__item .t-dropdown__item-content) {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  width: 100% !important;
  justify-content: flex-start !important;
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
}

:deep(.t-dropdown__item svg),
:deep(.t-dropdown__item .t-icon) {
  width: 20px !important;
  height: 20px !important;
  font-size: 20px !important;
  flex-shrink: 0 !important;
}

/* 下拉菜单容器 */
:deep(.t-dropdown__menu-wrapper) {
  .t-dropdown__menu {
    padding: 8px !important;
    width: 160px !important;
    min-width: 160px !important;
  }
}
</style>
