<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <t-tag theme="primary" variant="light" class="hero-badge">
          🚀 全新无限邮箱体验
        </t-tag>
        <h1 class="hero-title">
          下一代
          <span class="highlight"> 无限邮箱 </span>
          系统
        </h1>
        <p class="hero-description">
          体验前所未有的邮件管理方式。安全、快速、智能的无限邮箱系统， 让您随时随地高效处理邮件，提升工作效率。
        </p>
        <div class="hero-actions">
          <t-button size="large" theme="primary" @click="handleRegister">
            免费开始使用
            <template #suffix>
              <t-icon name="arrow-right" />
            </template>
          </t-button>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="features-container">
        <div class="section-header">
          <h2 class="section-title">核心功能优势</h2>
          <p class="section-description">
            MailCode 无限邮箱系统，六大核心功能重新定义邮件体验，为您提供前所未有的邮件管理解决方案
          </p>
        </div>

        <div class="features-grid">
          <t-card
            v-for="feature in features"
            :key="feature.id"
            class="feature-card"
            hover
          >
            <template #header>
              <div class="feature-icon" :class="feature.iconClass">
                <component :is="iconComponents[feature.icon]" :size="28" />
              </div>
            </template>
            <template #title>{{ feature.title }}</template>
            <div class="feature-description">{{ feature.description }}</div>
          </t-card>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
      <div class="stats-container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">1M+</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">系统可用性</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24/7</div>
            <div class="stat-label">技术支持</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">150+</div>
            <div class="stat-label">国家和地区</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="cta-container">
        <h2 class="cta-title">准备好体验未来的邮箱了吗？</h2>
        <p class="cta-description">加入数百万用户的行列，开始使用MailCode提升您的邮件管理效率</p>
        <div class="cta-actions">
          <t-button size="large" theme="primary" @click="handleRegister">
            立即注册
            <template #suffix>
              <t-icon name="arrow-right" />
            </template>
          </t-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Infinity, Lock, Rocket, Mail, Database, Smartphone } from 'lucide-vue-next'

const router = useRouter()

// 注册图标组件
const iconComponents = {
  Infinity,
  Lock,
  Rocket,
  Mail,
  Database,
  Smartphone
}

const features = ref([
  {
    id: 1,
    icon: 'Infinity',
    iconClass: 'icon-blue',
    title: '无限存储',
    description: '告别容量焦虑！提供真正的无限邮箱存储空间，海量邮件、附件随心保存，永不丢失重要信息'
  },
  {
    id: 2,
    icon: 'Lock',
    iconClass: 'icon-red',
    title: '极致安全',
    description: '银行级安全防护体系，多重加密算法保护，防钓鱼、防病毒、防泄露，让您的邮件固若金汤'
  },
  {
    id: 3,
    icon: 'Rocket',
    iconClass: 'icon-purple',
    title: '极速体验',
    description: '全球顶级服务器集群，毫秒级邮件收发，智能路由优化，无论何时何地都能享受闪电般的邮件体验'
  },
  {
    id: 4,
    icon: 'Mail',
    iconClass: 'icon-green',
    title: '智能邮件',
    description: 'AI驱动的智能邮件助手，自动分类、优先级排序、垃圾邮件过滤，让重要邮件永不错过'
  },
  {
    id: 5,
    icon: 'Database',
    iconClass: 'icon-orange',
    title: '数据备份',
    description: '多重数据备份机制，云端自动同步，历史邮件永久保存，支持一键导入导出，数据安全无忧'
  },
  {
    id: 6,
    icon: 'Smartphone',
    iconClass: 'icon-teal',
    title: '全端同步',
    description: '完美支持手机、平板、电脑等所有设备，实时同步邮件状态，随时随地高效办公'
  }
])

const handleRegister = () => {
  router.push('/register')
}




</script>

<style lang="less" scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

// Hero Section
.hero-section {
  padding: 80px 16px;
  text-align: center;

  @media (min-width: 640px) {
    padding: 80px 24px;
  }

  @media (min-width: 1024px) {
    padding: 80px 32px;
  }
}

.hero-container {
  max-width: 1280px;
  margin: 0 auto;
}

.hero-badge {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: 'Inter', 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  letter-spacing: -0.01em;

  @media (min-width: 768px) {
    font-size: 18px;
    padding: 10px 20px;
    border-radius: 24px;
  }

  @media (min-width: 1024px) {
    font-size: 20px;
    padding: 12px 24px;
    border-radius: 28px;
  }
}

.hero-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 24px;
  line-height: 1.2;

  @media (min-width: 768px) {
    font-size: 48px;
  }

  @media (min-width: 1024px) {
    font-size: 64px;
  }

  .highlight {
    color: #2563eb;
  }
}

.hero-description {
  font-size: 18px;
  color: #6b7280;
  margin-bottom: 32px;
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (min-width: 768px) {
    font-size: 20px;
  }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  align-items: center;

  @media (min-width: 640px) {
    flex-direction: row;
  }
}

// Features Section
.features-section {
  padding: 80px 16px;
  background: white;

  @media (min-width: 640px) {
    padding: 80px 24px;
  }

  @media (min-width: 1024px) {
    padding: 80px 32px;
  }
}

.features-container {
  max-width: 1280px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 64px;
}

.section-title {
  font-size: 24px;
  font-weight: 800;
  color: #111827;
  margin-bottom: 16px;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  letter-spacing: -0.04em;

  @media (min-width: 768px) {
    font-size: 32px;
  }

  @media (min-width: 1024px) {
    font-size: 40px;
  }
}

.section-description {
  font-size: 18px;
  color: #6b7280;
  max-width: 512px;
  margin: 0 auto;
  line-height: 1.6;
  font-family: 'Inter', 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
  letter-spacing: -0.01em;

  @media (min-width: 768px) {
    font-size: 20px;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 28px;
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
}

.feature-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 20px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  // 卡片标题样式
  :deep(.t-card__title) {
    font-size: 20px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 8px;
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    letter-spacing: -0.025em;

    @media (min-width: 768px) {
      font-size: 22px;
    }
  }
}

.feature-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }

  &.icon-blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.2);
  }

  &.icon-red {
    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
    color: #dc2626;
    box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.2);
  }

  &.icon-purple {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    color: #7c3aed;
    box-shadow: 0 4px 14px 0 rgba(124, 58, 237, 0.2);
  }

  &.icon-green {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #059669;
    box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.2);
  }

  &.icon-orange {
    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
    color: #ea580c;
    box-shadow: 0 4px 14px 0 rgba(234, 88, 12, 0.2);
  }

  &.icon-teal {
    background: linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%);
    color: #0d9488;
    box-shadow: 0 4px 14px 0 rgba(13, 148, 136, 0.2);
  }
}

.feature-description {
  color: #4b5563;
  line-height: 1.7;
  font-size: 15px;
  font-weight: 400;
  font-family: 'Inter', 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  letter-spacing: -0.01em;

  @media (min-width: 768px) {
    font-size: 16px;
    line-height: 1.8;
  }

  @media (min-width: 1024px) {
    font-size: 17px;
  }
}

// Stats Section
.stats-section {
  padding: 80px 16px;
  background: #2563eb;

  @media (min-width: 640px) {
    padding: 80px 24px;
  }

  @media (min-width: 1024px) {
    padding: 80px 32px;
  }
}

.stats-container {
  max-width: 1280px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  text-align: center;

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-item {
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: white;
    margin-bottom: 8px;

    @media (min-width: 768px) {
      font-size: 40px;
    }
  }

  .stat-label {
    color: #bfdbfe;
    font-size: 14px;

    @media (min-width: 768px) {
      font-size: 16px;
    }
  }
}

// CTA Section
.cta-section {
  padding: 80px 16px;

  @media (min-width: 640px) {
    padding: 80px 24px;
  }

  @media (min-width: 1024px) {
    padding: 80px 32px;
  }
}

.cta-container {
  max-width: 1024px;
  margin: 0 auto;
  text-align: center;
}

.cta-title {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 24px;

  @media (min-width: 768px) {
    font-size: 32px;
  }

  @media (min-width: 1024px) {
    font-size: 40px;
  }
}

.cta-description {
  font-size: 18px;
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;

  @media (min-width: 768px) {
    font-size: 20px;
  }
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  align-items: center;

  @media (min-width: 640px) {
    flex-direction: row;
  }
}
</style>
