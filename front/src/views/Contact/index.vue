<template>
  <div class="contact-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="header-section">
        <h1 class="page-title">联系我们</h1>
        <p class="page-description">
          我们随时为您提供帮助和支持
        </p>
      </div>

      <div class="content-layout">
        <!-- 联系信息 -->
        <div class="contact-info">
          <div class="info-card">
            <div class="info-icon">
              <t-icon name="mail" size="24px" />
            </div>
            <h3>邮箱支持</h3>
            <p><EMAIL></p>
            <p>我们会在24小时内回复您的邮件</p>
          </div>

          <div class="info-card">
            <div class="info-icon">
              <t-icon name="phone" size="24px" />
            </div>
            <h3>电话支持</h3>
            <p>************</p>
            <p>工作日 9:00-18:00</p>
          </div>

          <div class="info-card">
            <div class="info-icon">
              <t-icon name="location" size="24px" />
            </div>
            <h3>公司地址</h3>
            <p>北京市朝阳区科技园区</p>
            <p>MailCode大厦 10层</p>
          </div>

          <div class="info-card">
            <div class="info-icon">
              <t-icon name="time" size="24px" />
            </div>
            <h3>工作时间</h3>
            <p>周一至周五 9:00-18:00</p>
            <p>周六 10:00-16:00</p>
          </div>
        </div>

        <!-- 联系表单 -->
        <div class="contact-form-section">
          <t-card class="form-card">
            <template #header>
              <h2>发送消息</h2>
            </template>

            <t-form
              :data="formData"
              :rules="rules"
              ref="formRef"
              @submit="handleSubmit"
              class="contact-form"
            >
              <t-form-item label="姓名" name="name">
                <t-input
                  v-model="formData.name"
                  placeholder="请输入您的姓名"
                  size="large"
                />
              </t-form-item>

              <t-form-item label="邮箱" name="email">
                <t-input
                  v-model="formData.email"
                  placeholder="请输入您的邮箱地址"
                  type="email"
                  size="large"
                />
              </t-form-item>

              <t-form-item label="主题" name="subject">
                <t-select
                  v-model="formData.subject"
                  placeholder="请选择咨询主题"
                  size="large"
                >
                  <t-option value="technical" label="技术支持" />
                  <t-option value="billing" label="账单问题" />
                  <t-option value="feature" label="功能建议" />
                  <t-option value="bug" label="问题反馈" />
                  <t-option value="other" label="其他问题" />
                </t-select>
              </t-form-item>

              <t-form-item label="消息内容" name="message">
                <t-textarea
                  v-model="formData.message"
                  placeholder="请详细描述您的问题或建议..."
                  :autosize="{ minRows: 4, maxRows: 8 }"
                />
              </t-form-item>

              <t-button
                type="submit"
                theme="primary"
                size="large"
                block
                :loading="loading"
                class="submit-btn"
              >
                发送消息
              </t-button>
            </t-form>
          </t-card>
        </div>
      </div>

      <!-- FAQ 部分 -->
      <div class="faq-section">
        <h2 class="section-title">常见问题</h2>
        <div class="faq-list">
          <t-collapse v-model="activePanel">
            <t-collapse-panel
              v-for="faq in faqs"
              :key="faq.id"
              :value="faq.id"
              :header="faq.question"
            >
              <p>{{ faq.answer }}</p>
            </t-collapse-panel>
          </t-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

const formRef = ref()
const loading = ref(false)
const activePanel = ref([])

const formData = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const rules = {
  name: [
    { required: true, message: '请输入您的姓名', type: 'error' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', type: 'error' },
    { email: true, message: '请输入正确的邮箱格式', type: 'error' }
  ],
  subject: [
    { required: true, message: '请选择咨询主题', type: 'error' }
  ],
  message: [
    { required: true, message: '请输入消息内容', type: 'error' },
    { min: 10, message: '消息内容不能少于10个字符', type: 'error' }
  ]
}

const faqs = ref([
  {
    id: '1',
    question: '如何创建新的邮箱账户？',
    answer: '您可以在"获取邮箱"页面快速生成新的邮箱地址，支持多种域名选择，生成后即可立即使用。'
  },
  {
    id: '2',
    question: '邮箱存储空间有限制吗？',
    answer: '我们提供无限存储空间，您可以放心保存所有重要邮件和附件，无需担心容量问题。'
  },
  {
    id: '3',
    question: '如何设置邮件转发？',
    answer: '在"我的邮箱"页面中，选择要设置的邮箱，点击"管理"按钮，即可配置邮件转发规则。'
  },
  {
    id: '4',
    question: '支持哪些邮件客户端？',
    answer: '我们支持所有主流邮件客户端，包括Outlook、Apple Mail、Thunderbird等，提供详细的配置指南。'
  },
  {
    id: '5',
    question: '如何保证邮件安全？',
    answer: '我们采用银行级加密技术，支持SSL/TLS加密传输，并提供垃圾邮件过滤和病毒扫描功能。'
  }
])

const handleSubmit = async ({ validateResult }) => {
  if (validateResult === true) {
    loading.value = true
    try {
      // 模拟提交请求
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      MessagePlugin.success('消息发送成功！我们会尽快回复您。')
      
      // 重置表单
      Object.keys(formData).forEach(key => {
        formData[key] = ''
      })
      formRef.value.reset()
    } catch (error) {
      MessagePlugin.error('发送失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }
}
</script>

<style lang="less" scoped>
.contact-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 16px;

  @media (min-width: 640px) {
    padding: 60px 24px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 60px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    font-size: 40px;
  }
}

.page-description {
  font-size: 18px;
  color: #6b7280;
  margin: 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;
  margin-bottom: 80px;

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.info-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
}

.info-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: white;
}

.info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.info-card p {
  color: #6b7280;
  margin: 4px 0;
  font-size: 14px;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-form {
  padding: 0;
}

.submit-btn {
  margin-top: 16px;
}

.faq-section {
  margin-top: 80px;
}

.section-title {
  font-size: 28px;
  font-weight: bold;
  color: #111827;
  text-align: center;
  margin-bottom: 40px;
}

.faq-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
