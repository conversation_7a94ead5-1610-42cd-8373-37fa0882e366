<template>
  <div class="mailbox-page">
    <!-- Main Content -->
    <main class="main-content">
      <div class="content-layout">
        <!-- Email Generator Section - Left Sidebar -->
        <aside class="generator-sidebar">
          <div class="generator-section">
            <div class="section-header">
              <div class="title-with-icon">
                <div class="title-icon-wrapper generator-icon">
                  <Settings :size="24" class="title-icon" />
                </div>
                <h2 class="section-title">邮箱生成器</h2>
              </div>
              <p class="section-description">
                快速生成邮箱地址，保护您的隐私安全
              </p>
              <div class="feature-badges">
                <div class="feature-item">
                  <Shield :size="16" />
                  <span>安全防护</span>
                </div>
                <div class="feature-item">
                  <Zap :size="16" />
                  <span>即时生成</span>
                </div>
                <div class="feature-item">
                  <Infinity :size="16" />
                  <span>无限使用</span>
                </div>
              </div>
            </div>

            <div class="generator-container">
              <!-- Email Generation Area -->
              <div class="generator-card">
                <!-- Email Input Display -->
                <div class="email-input-display">
                  <div class="input-display-row">
                    <div class="prefix-display">
                      {{ emailPrefix }}
                    </div>
                    <span class="at-symbol">@</span>
                    <div class="domain-select-wrapper">
                      <t-select
                        v-model="selectedDomain"
                        placeholder="选择域名"
                        size="large"
                        class="domain-selector"
                      >
                        <t-option
                          v-for="domain in domains"
                          :key="domain"
                          :value="domain"
                          :label="domain"
                        />
                      </t-select>
                    </div>
                  </div>
                </div>

                <!-- Generated Email Display -->
                <div class="email-preview-card">
                  <div class="preview-content">
                    <div class="preview-icon">
                      <Mail :size="20" />
                    </div>
                    <div class="preview-email">{{ generatedEmail }}</div>
                    <t-button
                      theme="primary"
                      size="medium"
                      @click="copyEmail"
                      class="copy-email-btn"
                    >
                      <Copy :size="16" />
                      复制邮箱
                    </t-button>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                  <t-button
                    theme="success"
                    size="large"
                    @click="generateRandomEmail"
                    class="generate-btn"
                  >
                    <Shuffle :size="18" />
                    随机生成
                  </t-button>
                  <t-button
                    theme="primary"
                    size="large"
                    @click="refreshInbox"
                    class="refresh-btn"
                  >
                    <RefreshCw :size="18" />
                    刷新收件箱
                  </t-button>
                </div>

                <!-- Add to My Mailbox Button -->
                <div class="add-to-my-mailbox">
                  <t-button
                    theme="primary"
                    variant="base"
                    size="large"
                    @click="addToMyMailbox"
                    class="add-mailbox-btn"
                  >
                    <template #icon>
                      <Plus :size="18" />
                    </template>
                    添加到我的邮箱
                  </t-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 使用提示卡片 -->
          <div class="tips-card">
            <div class="tips-header">
              <div class="tips-icon-wrapper">
                <Lightbulb :size="20" class="tips-icon" />
              </div>
              <h4 class="tips-title">使用提示</h4>
            </div>
            <div class="tips-content">
              <div class="tip-item">
                <div class="tip-number">1</div>
                <div class="tip-text">生成的邮箱地址可长期使用，无过期限制</div>
              </div>
              <div class="tip-item">
                <div class="tip-number">2</div>
                <div class="tip-text">支持接收各类邮件，包括验证码、通知等</div>
              </div>
              <div class="tip-item">
                <div class="tip-number">3</div>
                <div class="tip-text">邮件实时推送，自动刷新收件箱</div>
              </div>
              <div class="tip-item">
                <div class="tip-number">4</div>
                <div class="tip-text">保护个人隐私，避免泄露真实邮箱</div>
              </div>
            </div>
          </div>
        </aside>

        <!-- Smart Inbox Section - Main Content -->
        <main class="inbox-main">
          <section class="inbox-section">
            <div class="inbox-header">
              <div class="inbox-title-area">
                <div class="inbox-icon-wrapper">
                  <Inbox :size="24" class="inbox-icon" />
                </div>
                <h3 class="inbox-title">智能收件箱</h3>
                <t-tag theme="primary" variant="light" class="email-count">
                  {{ emails.length }} 封邮件
                </t-tag>

              </div>
              <div class="inbox-actions">
                <span class="last-update">
                  最后更新: {{ lastUpdateTime }}
                </span>
                <t-button
                  size="small"
                  theme="default"
                  variant="text"
                  @click="refreshInbox"
                  class="inbox-refresh-btn"
                >
                  <RefreshCw :size="16" />
                </t-button>
              </div>
            </div>

        <!-- Email List -->
        <div class="email-list-container">
          <div v-if="emails.length === 0" class="empty-inbox">
            <t-icon name="mail" size="64px" class="empty-icon" />
            <h4 class="empty-title">收件箱为空</h4>
            <p class="empty-description">使用上方生成的邮箱地址接收邮件</p>
          </div>

          <div v-else class="email-list">
            <div
              v-for="email in emails"
              :key="email.id"
              class="email-item"
              @click="toggleEmailDetail(email.id)"
              :class="{ expanded: expandedEmails.includes(email.id) }"
            >
              <div class="email-header">
                <div class="sender-info">
                  <div class="sender-avatar">
                    {{ email.sender.charAt(0).toUpperCase() }}
                  </div>
                  <div class="sender-details">
                    <h4 class="sender-name">{{ email.sender }}</h4>
                    <p class="sender-email">{{ email.senderEmail }}</p>
                  </div>
                </div>
                <div class="email-meta">
                  <t-tag
                    v-if="!email.read"
                    theme="primary"
                    size="small"
                    class="unread-badge"
                  >
                    新
                  </t-tag>
                  <span class="email-time">{{ email.time }}</span>
                  <t-icon
                    name="chevron-down"
                    class="expand-icon"
                    :class="{ rotated: expandedEmails.includes(email.id) }"
                  />
                </div>
              </div>

              <h3 class="email-subject">{{ email.subject }}</h3>

              <div
                v-if="email.verificationCode"
                class="verification-section"
              >
                <div class="verification-content">
                  <span class="verification-label">验证码</span>
                  <div class="verification-code-area">
                    <code class="verification-code">{{ email.verificationCode }}</code>
                    <t-button
                      size="small"
                      theme="primary"
                      variant="text"
                      @click.stop="copyVerificationCode(email.verificationCode)"
                      class="copy-code-btn"
                    >
                      <t-icon name="file-copy" />
                    </t-button>
                  </div>
                </div>
              </div>

              <p class="email-preview">{{ email.preview }}</p>

              <!-- Email Detail -->
              <div
                v-if="expandedEmails.includes(email.id)"
                class="email-detail"
              >
                <div class="email-content" v-html="email.content"></div>
                <div class="email-actions">
                  <t-button size="small" theme="default" variant="text">
                    <t-icon name="chat" />
                    回复
                  </t-button>
                  <t-button size="small" theme="danger" variant="text">
                    <t-icon name="delete" />
                    删除
                  </t-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { MessagePlugin } from 'tdesign-vue-next';
import { Shield, Zap, Infinity, Mail, Copy, Shuffle, RefreshCw, Inbox, AtSign, MailOpen, Settings, Lightbulb, Plus } from 'lucide-vue-next';

const emailPrefix = ref("");
const selectedDomain = ref("temp-mail.org");
const expandedEmails = ref([]);
const lastUpdateTime = ref("");

const domains = [
  "temp-mail.org",
  "10minutemail.com",
  "guerrillamail.com",
  "mailinator.com",
  "tempmail.net",
  "throwaway.email",
];

const emails = ref([
  {
    id: 1,
    sender: "Amazon",
    senderEmail: "<EMAIL>",
    subject: "您的订单确认 - 订单号 #12345678",
    preview: "感谢您在 Amazon 购物！您的订单已确认，预计送达时间为...",
    content:
      "<p>尊敬的用户：</p><p>感谢您在 Amazon 购物！</p><p>您的订单 #12345678 已确认。以下是订单详情：</p><ul><li>预计送达时间：3-5 个工作日</li><li>支付方式：信用卡支付</li><li>订单金额：￥299.00</li></ul><p>您可以随时在您的账户中查看订单状态。</p><p>如有任何问题，请联系我们的客服团队。</p><p>祝您购物愉快！</p><p>Amazon 团队</p>",
    time: "2分钟前",
    read: false,
    verificationCode: "AMZ123456",
  },
  {
    id: 2,
    sender: "Netflix",
    senderEmail: "<EMAIL>",
    subject: "确认您的 Netflix 账户 - 验证码",
    preview: "为了确保您的账户安全，请输入以下验证码完成注册...",
    content:
      '<p>亲爱的用户：</p><p>感谢您注册 Netflix！</p><p>为了确保您的账户安全，请在注册页面输入以下验证码：</p><p style="font-size: 24px; font-weight: bold; color: #E50914; margin: 20px 0;">NFX789012</p><p>此验证码将在 10 分钟后失效。</p><p>如果这不是您的操作，请忽略此邮件。</p><p>享受您的观影时光！</p><p>Netflix 团队</p>',
    verificationCode: "NFX789012",
    time: "5分钟前",
    read: true,
  },
  {
    id: 3,
    sender: "LinkedIn",
    senderEmail: "<EMAIL>",
    subject: "新的工作机会：高级前端开发工程师",
    preview: "根据您的职业背景，我们为您推荐了一个很适合您的职位...",
    content:
      "<p>您好！</p><p>我们发现了一个很适合您的职位：</p><h3>高级前端开发工程师 - 字节跳动</h3><p><strong>职位描述：</strong></p><ul><li>负责公司核心产品的前端开发</li><li>参与技术架构设计和优化</li><li>年薪范围：40-80万</li><li>工作地点：北京/上海</li></ul><p><strong>要求：</strong></p><ul><li>5年以上前端开发经验</li><li>精通 Vue/React 等主流框架</li><li>良好的团队协作能力</li></ul><p>如果您对这个职位感兴趣，请点击下方按钮查看详情。</p><p>祝您求职顺利！</p><p>LinkedIn 职业团队</p>",
    time: "1小时前",
    read: true,
  },
  {
    id: 4,
    sender: "Apple",
    senderEmail: "<EMAIL>",
    subject: "Apple ID 安全提醒",
    preview: "我们检测到您的账户在新设备上登录，请确认是否为您本人操作...",
    content:
      "<p>尊敬的 Apple 用户：</p><p>我们检测到您的 Apple ID 在一台新的 iPhone 设备上登录。</p><p><strong>登录详情：</strong></p><ul><li>设备：iPhone 14 Pro</li><li>位置：北京，中国</li><li>时间：2025年8月2日 14:30</li></ul><p>如果这是您本人的操作，请忽略此邮件。</p><p>如果这不是您的操作，请立即：</p><ul><li>更改您的密码</li><li>开启双重认证</li><li>联系 Apple 支持</li></ul><p>保护您的账户安全对我们来说至关重要。</p><p>Apple 安全团队</p>",
    time: "2小时前",
    read: false,
    verificationCode: "APL456789",
  },
]);

const generatedEmail = computed(() => {
  return `${emailPrefix.value}@${selectedDomain.value}`;
});

const generateRandomEmail = () => {
  // 生成15位随机字符串
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';
  for (let i = 0; i < 15; i++) {
    randomString += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  // 每5位之间用"-"分割
  const formattedString = randomString.match(/.{1,5}/g).join('-');
  emailPrefix.value = formattedString;
};

const copyEmail = async () => {
  try {
    await navigator.clipboard.writeText(generatedEmail.value);
    MessagePlugin.success('邮箱地址已复制到剪贴板！');
  } catch (err) {
    console.error("复制失败:", err);
    MessagePlugin.error('复制失败，请手动复制');
  }
};

const copyVerificationCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code);
    MessagePlugin.success('验证码已复制到剪贴板！');
  } catch (err) {
    console.error("复制失败:", err);
    MessagePlugin.error('复制失败，请手动复制');
  }
};

const toggleEmailDetail = (emailId) => {
  const index = expandedEmails.value.indexOf(emailId);
  if (index > -1) {
    expandedEmails.value.splice(index, 1);
  } else {
    expandedEmails.value.push(emailId);
    // Mark as read
    const email = emails.value.find((e) => e.id === emailId);
    if (email) {
      email.read = true;
    }
  }
};

const refreshInbox = () => {
  lastUpdateTime.value = new Date().toLocaleString("zh-CN");
  MessagePlugin.info('正在刷新收信箱...');

  // 模拟刷新，随机添加新邮件
  setTimeout(() => {
    if (Math.random() > 0.3) {
      const senders = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      const subjects = [
        'Facebook 登录验证',
        'Apple ID 安全提醒',
        'LinkedIn 连接请求',
        'PayPal 交易确认',
        'Instagram 新关注者'
      ];
      const randomSender = senders[Math.floor(Math.random() * senders.length)];
      const randomSubject = subjects[Math.floor(Math.random() * subjects.length)];

      const newEmail = {
        id: emails.value.length + 1,
        sender: randomSender.split('@')[0],
        senderEmail: randomSender,
        subject: randomSubject,
        preview: '这是一封新收到的邮件，用于演示刷新功能...',
        content: '这是一封新收到的邮件，用于演示刷新功能。邮件内容会根据实际情况显示，包含重要的通知信息。',
        time: '刚刚',
        verificationCode: Math.random() > 0.6 ? Math.floor(Math.random() * 900000 + 100000).toString() : null,
        read: false
      };
      emails.value.unshift(newEmail);
      MessagePlugin.success('收到新邮件');
    } else {
      MessagePlugin.info('暂无新邮件');
    }
  }, 1000);
};

const addToMyMailbox = () => {
  // 添加到我的邮箱功能
  MessagePlugin.success('邮箱已添加到我的邮箱列表！');

  // 这里可以添加实际的保存逻辑，比如：
  // 1. 保存到本地存储
  // 2. 发送到后端API
  // 3. 更新用户的邮箱列表

  console.log('添加邮箱到我的邮箱:', generatedEmail.value);
};

// 定时器引用
let refreshTimer = null;

onMounted(() => {
  // 页面加载时自动生成随机邮箱前缀
  generateRandomEmail();
  lastUpdateTime.value = new Date().toLocaleString("zh-CN");
  // Auto refresh every 30 seconds
  refreshTimer = setInterval(() => {
    refreshInbox();
  }, 30000);
});

onUnmounted(() => {
  // 清除定时器，防止在其他页面继续刷新
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});
</script>

<style scoped>
.mailbox-page {
  min-height: 100vh;
  background: #ffffff;
}

.main-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 24px;
  height: 100vh;
  overflow: hidden;
}

.content-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* Generator Sidebar */
.generator-sidebar {
  width: 500px;
  flex-shrink: 0;
}

.generator-section {
  background: #f8f9fa;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 32px 24px;
  height: fit-content;
  margin-bottom: 24px;
}

/* Tips Card */
.tips-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.tips-icon-wrapper {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.25);
}

.tips-icon {
  color: white;
}

.tips-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.tip-number {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2px;
}

.tip-text {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
  flex: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 32px;
}

.title-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 12px;
}

.title-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.25);
  transition: all 0.3s ease;
}

.title-icon-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.35);
}

.title-icon {
  color: white;
  transition: transform 0.3s ease;
}

.title-icon-wrapper:hover .title-icon {
  transform: scale(1.1);
}

.section-title {
  font-size: 28px;
  font-weight: 800;
  color: #1f2937;
  margin: 0;
  letter-spacing: -0.02em;
}

.section-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.feature-badges {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #3b82f6;
  font-weight: 600;
  font-size: 14px;
}

.generator-container {
  width: 100%;
}

.generator-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 28px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.email-input-display {
  margin-bottom: 24px;
}

.input-display-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  width: 100%;
}

.prefix-display {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  min-width: 180px;
  max-width: 200px;
  text-align: center;
  flex-shrink: 0;
}

.at-symbol {
  font-size: 20px;
  font-weight: bold;
  color: #6b7280;
  flex-shrink: 0;
}

.domain-select-wrapper {
  flex: 1;
  min-width: 0;
  max-width: 200px;
}

.domain-selector {
  width: 100%;
}

/* Inbox Main Content */
.inbox-main {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.email-preview-card {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  text-align: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.preview-icon {
  color: #3b82f6;
  font-size: 20px;
}

.preview-email {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  line-height: 1.4;
  margin: 8px 0;
}

.copy-email-btn {
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 40px;
  line-height: 1;
}

.copy-email-btn svg {
  vertical-align: middle;
  margin-top: -1px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.add-to-my-mailbox {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.add-mailbox-btn {
  width: 100%;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  height: 48px;
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

.add-mailbox-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

.add-mailbox-btn .t-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-mailbox-btn .t-icon {
  display: flex;
  align-items: center;
}

.generate-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 48px;
  line-height: 1;
}

.generate-btn svg {
  vertical-align: middle;
  margin-top: -1px;
}

.refresh-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 48px;
  line-height: 1;
}

.refresh-btn svg {
  vertical-align: middle;
  margin-top: -1px;
}

/* Inbox Section */
.inbox-section {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.inbox-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.inbox-title-area {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.inbox-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.25);
  transition: all 0.3s ease;
}

.inbox-icon-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35);
}

.inbox-icon {
  color: white;
  transition: transform 0.3s ease;
}

.inbox-icon-wrapper:hover .inbox-icon {
  transform: scale(1.1);
}

.inbox-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.email-count {
  margin-left: 8px;
}

.filter-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.filter-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-btn {
  border-radius: 16px;
}

.inbox-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.last-update {
  font-size: 14px;
  color: #6b7280;
}

.inbox-refresh-btn {
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
}

.inbox-refresh-btn svg {
  vertical-align: middle;
}

/* Email List */
.email-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.empty-inbox {
  text-align: center;
  padding: 64px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-icon {
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.empty-description {
  color: #9ca3af;
  margin: 0;
}

.email-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.email-item {
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.email-item:hover {
  background: #f8fafc;
}

.email-item.expanded {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.02) 100%);
}

.email-item:last-child {
  border-bottom: none;
}

.email-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sender-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.sender-details h4 {
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.sender-details p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.email-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.unread-badge {
  animation: pulse 2s infinite;
}

.email-time {
  font-size: 14px;
  color: #9ca3af;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.email-subject {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.verification-section {
  margin: 16px 0;
  background: #dbeafe;
  border: 1px solid #3b82f6;
  border-radius: 12px;
  padding: 16px;
}

.verification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.verification-label {
  color: #1d4ed8;
  font-weight: 600;
}

.verification-code-area {
  display: flex;
  align-items: center;
  gap: 8px;
}

.verification-code {
  font-size: 24px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: bold;
  color: #1d4ed8;
}

.copy-code-btn {
  border-radius: 8px;
}

.email-preview {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 16px;
}

.email-detail {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.email-content {
  background: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  color: #374151;
  line-height: 1.6;
}

.email-content p {
  margin-bottom: 16px;
}

.email-content ul {
  margin: 16px 0;
  padding-left: 24px;
}

.email-content li {
  margin-bottom: 8px;
}

.email-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 24px 0 16px 0;
}

.email-content strong {
  font-weight: 600;
  color: #1f2937;
}

.email-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive */
@media (max-width: 1200px) {
  .generator-sidebar {
    width: 480px;
  }
}

@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
    height: auto;
  }

  .generator-sidebar {
    width: 100%;
    order: 2;
  }

  .inbox-main {
    order: 1;
    height: 70vh;
  }

  .generator-section {
    height: auto;
    padding: 24px 20px;
  }

  .section-title {
    font-size: 24px;
  }

  .feature-badges {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
    height: auto;
  }

  .content-layout {
    gap: 16px;
  }

  .generator-sidebar {
    width: 100%;
  }

  .generator-section {
    padding: 20px 16px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-description {
    font-size: 14px;
  }

  .feature-badges {
    flex-direction: column;
    gap: 12px;
  }

  .generator-card {
    padding: 20px 16px;
  }

  .input-display-row {
    flex-direction: column;
    gap: 8px;
  }

  .prefix-display {
    min-width: auto;
    width: 100%;
  }

  .at-symbol {
    font-size: 20px;
  }

  .inbox-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 16px;
  }

  .inbox-title-area {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-buttons {
    margin-left: 0;
    margin-top: 8px;
  }

  .inbox-title {
    font-size: 24px;
  }

  .email-item {
    padding: 16px;
  }

  .inbox-main {
    height: 60vh;
  }
}
</style>
