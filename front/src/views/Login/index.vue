<template>
  <div class="login-page">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <div class="login-container">
      <div class="login-card">
        <!-- Logo区域 -->
        <div class="logo-section">
          <div class="logo-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <polyline points="22,6 12,13 2,6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1>MailCode</h1>
          <p>安全、高效的邮件管理平台</p>
        </div>

        <t-form
          :data="formData"
          :rules="rules"
          ref="formRef"
          @submit="handleLogin"
          class="login-form"
        >
          <t-form-item label="邮箱" name="email">
            <t-input
              v-model="formData.email"
              placeholder="请输入邮箱地址"
              type="email"
              size="large"
            />
          </t-form-item>

          <t-form-item label="密码" name="password">
            <t-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="large"
            />
          </t-form-item>

          <div class="form-options">
            <div class="remember-me">
              <t-checkbox v-model="formData.rememberMe">
                记住我
              </t-checkbox>
            </div>
            <a href="#" class="forgot-password">忘记密码？</a>
          </div>

          <t-form-item>
            <t-button
              theme="primary"
              type="submit"
              size="large"
              :loading="loading"
              block
              class="login-btn"
            >
              登录
            </t-button>
          </t-form-item>
        </t-form>

        <div class="divider">
          <span>或</span>
        </div>

        <div class="social-login">
          <t-button variant="outline" size="large" block class="social-btn wechat-btn">
            <div class="btn-content">
              <t-icon name="logo-wechat-stroke" size="20px" />
              <span>使用微信登录</span>
            </div>
          </t-button>
        </div>

        <div class="login-footer">
          <p>
            还没有账户？
            <router-link to="/register" class="register-link">立即注册</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { performLogin } from '@/http/auth'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

const formData = reactive({
  email: '',
  password: '',
  rememberMe: false
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ],
  password: [
    { required: true, message: '请输入密码' }
  ]
}

const handleLogin = async ({ validateResult }) => {
  if (validateResult === true) {
    loading.value = true
    try {
      // 调用实际的登录接口
      const response = await performLogin({
        email: formData.email,
        password: formData.password
      })

      // 处理登录结果
      if (response && response.code === 200) {
        // 检查是否有重定向参数
        const urlParams = new URLSearchParams(window.location.search)
        const redirectPath = urlParams.get('redirect')
        const targetPath = redirectPath ? decodeURIComponent(redirectPath) : '/my-mailbox'
        await router.push(targetPath)
        await MessagePlugin.success(response.message || "登录成功")
      } else {
        await MessagePlugin.error(response.message || "登录失败")
      }
    } catch (error) {
      console.error('登录失败:', error)
      const errorMessage = error.response?.data?.message || error.message || "登录失败，请稍后重试"
      await MessagePlugin.error(errorMessage)
    } finally {
      loading.value = false
    }
  }
}
</script>

<style lang="less" scoped>
.login-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  .circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.1);
    animation: float 6s ease-in-out infinite;

    &.circle-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.circle-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.circle-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-container {
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 2;
}

.login-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.logo-section {
  text-align: center;
  margin-bottom: 32px;

  .logo-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #3B82F6, #1D4ED8);
    border-radius: 16px;
    margin-bottom: 16px;
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25);
  }

  h1 {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
  }

  p {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
  }
}

.login-form {
  margin-bottom: 8px;
  width: 100%;

  :deep(.t-form-item) {
    margin-bottom: 20px;
    text-align: left;
    display: flex !important;
    flex-direction: column !important;
  }

  :deep(.t-form__label) {
    width: auto !important;
    margin-left: 0 !important;
    margin-bottom: 8px !important;
    text-align: left !important;
    justify-content: flex-start !important;
  }

  :deep(.t-form__controls) {
    margin-left: 0 !important;
    width: 100% !important;
  }

  :deep(.t-input) {
    border-radius: 8px;
    border: 1px solid #e5e7eb;

    &:focus-within {
      border-color: #3B82F6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  width: 100%;

  .remember-me {
    display: flex;
    align-items: center;

    :deep(.t-checkbox) {
      .t-checkbox__label {
        font-size: 14px;
        color: #6b7280;
        margin-left: 8px;
      }

      .t-checkbox__input {
        border-radius: 4px;
        border-color: #d1d5db;

        &:checked {
          background-color: #3B82F6;
          border-color: #3B82F6;
        }
      }
    }
  }

  .forgot-password {
    color: #3B82F6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
      color: #1D4ED8;
      text-decoration: underline;
    }
  }
}

.login-btn {
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  border: none;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25);
  }
}

.divider {
  text-align: center;
  margin: 8px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
  }

  span {
    background: white;
    padding: 0 16px;
    color: #9ca3af;
    font-size: 14px;
  }
}

.social-login {
  margin-bottom: 24px;

  .social-btn {
    height: 44px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      border-color: #d1d5db;
      background-color: #f9fafb;
    }

    &.wechat-btn {
      .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        height: 100%;

        :deep(.t-icon) {
          color: #07C160 !important;
          flex-shrink: 0;
        }

        span {
          line-height: 1;
        }
      }
    }
  }
}

.login-footer {
  text-align: center;

  p {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
  }

  .register-link {
    color: #3B82F6;
    text-decoration: none;
    font-weight: 600;
    margin-left: 4px;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .logo-section {
    .logo-icon {
      width: 64px;
      height: 64px;
    }

    h1 {
      font-size: 28px;
    }
  }
}
</style>
