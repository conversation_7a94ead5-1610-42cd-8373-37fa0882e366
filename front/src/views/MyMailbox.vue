<template>
  <div class="my-mailbox-page">
    <div class="container">
      <div class="header-section">
        <h1 class="page-title">我的邮箱</h1>
        <p class="page-description">
          管理您的邮箱账户和设置
        </p>
      </div>

      <div class="content-section">
        <!-- 邮箱列表 -->
        <div class="mailbox-list">
          <t-card class="mailbox-card" v-for="mailbox in mailboxes" :key="mailbox.id">
            <template #header>
              <div class="mailbox-header">
                <div class="mailbox-info">
                  <h3 class="mailbox-email">{{ mailbox.email }}</h3>
                  <t-tag :theme="mailbox.status === 'active' ? 'success' : 'warning'">
                    {{ mailbox.status === 'active' ? '活跃' : '暂停' }}
                  </t-tag>
                </div>
                <div class="mailbox-actions">
                  <t-button size="small" variant="outline" @click="manageMailbox(mailbox)">
                    管理
                  </t-button>
                  <t-dropdown :options="getDropdownOptions(mailbox)">
                    <t-button size="small" variant="text">
                      <t-icon name="more" />
                    </t-button>
                  </t-dropdown>
                </div>
              </div>
            </template>

            <div class="mailbox-stats">
              <div class="stat-item">
                <div class="stat-label">存储使用</div>
                <div class="stat-value">
                  <t-progress
                    :percentage="mailbox.storageUsed / mailbox.storageTotal * 100"
                    size="small"
                  />
                  <span class="storage-text">
                    {{ formatStorage(mailbox.storageUsed) }} / {{ formatStorage(mailbox.storageTotal) }}
                  </span>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-label">今日邮件</div>
                <div class="stat-value">{{ mailbox.todayEmails }}</div>
              </div>

              <div class="stat-item">
                <div class="stat-label">未读邮件</div>
                <div class="stat-value">{{ mailbox.unreadEmails }}</div>
              </div>

              <div class="stat-item">
                <div class="stat-label">创建时间</div>
                <div class="stat-value">{{ formatDate(mailbox.createdAt) }}</div>
              </div>
            </div>
          </t-card>

          <!-- 添加新邮箱按钮 -->
          <t-card class="add-mailbox-card" hover @click="addNewMailbox">
            <div class="add-mailbox-content">
              <t-icon name="add" size="48px" />
              <h3>添加新邮箱</h3>
              <p>创建更多邮箱账户</p>
            </div>
          </t-card>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h2>快速操作</h2>
          <div class="actions-grid">
            <div class="action-item" @click="openWebmail">
              <t-icon name="mail" size="24px" />
              <span>打开网页邮箱</span>
            </div>
            <div class="action-item" @click="downloadApp">
              <t-icon name="mobile" size="24px" />
              <span>下载移动应用</span>
            </div>
            <div class="action-item" @click="configureClient">
              <t-icon name="setting" size="24px" />
              <span>配置邮件客户端</span>
            </div>
            <div class="action-item" @click="viewHelp">
              <t-icon name="help-circle" size="24px" />
              <span>帮助文档</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const mailboxes = ref([
  {
    id: 1,
    email: '<EMAIL>',
    status: 'active',
    storageUsed: 2.5 * 1024 * 1024 * 1024, // 2.5GB
    storageTotal: 10 * 1024 * 1024 * 1024, // 10GB
    todayEmails: 15,
    unreadEmails: 3,
    createdAt: new Date('2024-01-15')
  },
  {
    id: 2,
    email: '<EMAIL>',
    status: 'active',
    storageUsed: 1.2 * 1024 * 1024 * 1024, // 1.2GB
    storageTotal: 50 * 1024 * 1024 * 1024, // 50GB
    todayEmails: 28,
    unreadEmails: 7,
    createdAt: new Date('2024-02-01')
  }
])

const getDropdownOptions = (mailbox) => {
  return [
    { content: '编辑设置', value: 'edit' },
    { content: '查看详情', value: 'details' },
    { content: '暂停邮箱', value: 'suspend', disabled: mailbox.status !== 'active' },
    { content: '删除邮箱', value: 'delete', theme: 'error' }
  ]
}

const formatStorage = (bytes) => {
  const gb = bytes / (1024 * 1024 * 1024)
  return `${gb.toFixed(1)}GB`
}

const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN')
}

const manageMailbox = (mailbox) => {
  console.log('管理邮箱:', mailbox.email)
}

const addNewMailbox = () => {
  router.push('/get-mailbox')
}

const openWebmail = () => {
  console.log('打开网页邮箱')
}

const downloadApp = () => {
  console.log('下载移动应用')
}

const configureClient = () => {
  console.log('配置邮件客户端')
}

const viewHelp = () => {
  console.log('查看帮助文档')
}
</script>

<style lang="less" scoped>
.my-mailbox-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 16px;

  @media (min-width: 640px) {
    padding: 60px 24px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    font-size: 40px;
  }
}

.page-description {
  font-size: 18px;
  color: #6b7280;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
}

.mailbox-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mailbox-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mailbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mailbox-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .mailbox-email {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
}

.mailbox-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mailbox-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-top: 20px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-item {
  .stat-label {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 4px;
  }

  .stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;

    .storage-text {
      font-size: 12px;
      color: #6b7280;
      margin-left: 8px;
    }
  }
}

.add-mailbox-card {
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #2563eb;
    background: #f8fafc;
  }
}

.add-mailbox-content {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  h3 {
    margin: 16px 0 8px;
    color: #374151;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.quick-actions {
  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 20px;
  }
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  span {
    font-weight: 500;
    color: #374151;
  }
}
</style>
