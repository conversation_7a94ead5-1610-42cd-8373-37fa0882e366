<template>
  <div class="register-page">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <div class="register-container">
      <div class="register-card">
        <!-- Logo区域 -->
        <div class="logo-section">
          <div class="logo-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <polyline points="22,6 12,13 2,6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1>注册 MailCode</h1>
          <p>创建您的无限邮箱账户</p>
        </div>

        <t-form
          :data="formData"
          :rules="rules"
          ref="formRef"
          @submit="handleRegister"
          class="register-form"
        >
          <t-form-item label="用户名" name="username">
            <t-input
              v-model="formData.username"
              placeholder="请输入用户名"
              size="large"
            />
          </t-form-item>

          <t-form-item label="邮箱" name="email">
            <t-input
              v-model="formData.email"
              placeholder="请输入邮箱地址"
              type="email"
              size="large"
            />
          </t-form-item>

          <t-form-item label="密码" name="password">
            <t-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="large"
            />
          </t-form-item>

          <t-form-item label="确认密码" name="confirmPassword">
            <t-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              size="large"
            />
          </t-form-item>

          <div class="form-options">
            <t-checkbox v-model="formData.agreeTerms">
              我已阅读并同意
              <t-link theme="primary" @click="showTerms">《服务条款》</t-link>
              和
              <t-link theme="primary" @click="showPrivacy">《隐私政策》</t-link>
            </t-checkbox>
          </div>

          <t-button
            type="submit"
            theme="primary"
            size="large"
            block
            :loading="loading"
            class="register-btn"
          >
            注册
          </t-button>
        </t-form>

        <!-- 分割线 -->
        <div class="divider">
          <span>或</span>
        </div>

        <!-- 第三方注册 -->
        <div class="social-register">
          <t-button variant="outline" size="large" block class="social-btn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
              <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
              <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
              <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
            </svg>
            使用 Google 注册
          </t-button>
        </div>

        <!-- 登录链接 -->
        <div class="login-link">
          <span>已有账户？</span>
          <t-link theme="primary" @click="handleLogin">立即登录</t-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const formRef = ref()
const loading = ref(false)

const formData = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', type: 'error' },
    { min: 3, message: '用户名长度不能少于3位', type: 'error' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', type: 'error' },
    { email: true, message: '请输入正确的邮箱格式', type: 'error' }
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error' },
    { min: 6, message: '密码长度不能少于6位', type: 'error' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', type: 'error' },
    {
      validator: (val) => val === formData.password,
      message: '两次输入的密码不一致',
      type: 'error'
    }
  ]
}

const handleRegister = async ({ validateResult }) => {
  if (validateResult === true) {
    if (!formData.agreeTerms) {
      MessagePlugin.error('请先同意服务条款和隐私政策')
      return
    }

    loading.value = true
    try {
      // 模拟注册请求
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      MessagePlugin.success('注册成功！')
      router.push('/login')
    } catch (error) {
      MessagePlugin.error('注册失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }
}

const handleLogin = () => {
  router.push('/login')
}

const showTerms = () => {
  MessagePlugin.info('服务条款页面开发中...')
}

const showPrivacy = () => {
  MessagePlugin.info('隐私政策页面开发中...')
}
</script>

<style lang="less" scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.register-container {
  width: 100%;
  max-width: 400px;
  z-index: 1;
}

.register-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.logo-section {
  text-align: center;
  margin-bottom: 32px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.logo-section h1 {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.logo-section p {
  color: #6b7280;
  margin: 0;
}

.register-form {
  margin-bottom: 24px;
}

.form-options {
  margin-bottom: 24px;
}

.register-btn {
  margin-bottom: 24px;
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #6b7280;
  font-size: 14px;
}

.social-register {
  margin-bottom: 24px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-link {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

.login-link span {
  margin-right: 8px;
}
</style>
